name: Build
on: [ pull_request, push, workflow_dispatch ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@main
      - name: Validate gradle wrapper
        uses: gradle/wrapper-validation-action@main
      - name: Setup JDK 17
        uses: actions/setup-java@main
        with:
          java-version: 17
          distribution: 'zulu'
      - name: Make gradle wrapper executable
        run: chmod +x ./gradlew
      - name: Generate TypeScript schema classes
        run: ./gradlew generateTypeScriptSchemaClasses
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Setup Node packages
        run: npm ci --prefix buildSrc/src/main/resources/website
      - name: Build Angular
        run: npm run build --prefix buildSrc/src/main/resources/website
      - name: Generate Java schema classes
        run: ./gradlew generateJavaSchemaClasses
      - name: Build
        run: ./gradlew build buildSrc:build -x test
      # Tests don't work for some reason
      # - name: Run JUnits
      #   run: ./gradlew test
      - name: Capture main build artifact
        uses: actions/upload-artifact@main
        with:
          name: Main
          path: build/libs/
      - name: Capture JSON schema generator build artifact
        uses: actions/upload-artifact@main
        with:
          name: JSON Schema Generator
          path: buildSrc/build/libs/
