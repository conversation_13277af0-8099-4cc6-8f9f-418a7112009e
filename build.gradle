import org.mtr.core.Generator
import org.mtr.core.WebserverSetup

plugins {
	id "java"
	id "maven-publish"
}

group "org.mtr.core"
version rootProject.properties.version

repositories {
	mavenCentral()
	maven { url = "https://repo.mikeprimm.com/" }
	flatDir { dirs "libs" }
}

dependencies {
	implementation "org.mtr:Shadow-Libraries-net:0.0.1"
	implementation "org.mtr:Shadow-Libraries-util:0.0.1"
	implementation "com.google.code.gson:gson:+"
	implementation "it.unimi.dsi:fastutil:+"
	implementation "commons-io:commons-io:+"
	implementation "org.apache.logging.log4j:log4j-core:2.+"
	compileOnly "com.google.code.findbugs:jsr305:+"
	compileOnly "xyz.jpenilla:squaremap-api:+"
	compileOnly "net.kyori:adventure-api:+"
	compileOnly "us.dynmap:DynmapCoreAPI:3.7-beta-6"
	testImplementation "org.junit.jupiter:junit-jupiter-api:+"
	testImplementation "org.junit.platform:junit-platform-launcher:+"
	testImplementation "org.apache.httpcomponents:httpclient:+"
	testCompileOnly "com.google.code.findbugs:jsr305:+"
	testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:+"
}

java {
	toolchain {
		languageVersion.set(JavaLanguageVersion.of(8))
	}
	withSourcesJar()
	withJavadocJar()
}

publishing {
	publications {
		maven(MavenPublication) {
			from components.java
		}
	}
}

jar {
	manifest {
		attributes("Main-Class": "org.mtr.core.Main")
	}
}

test {
	useJUnitPlatform()
	testLogging { showStandardStreams = true }
}

tasks.register("generateTypeScriptSchemaClasses") {
	Generator.generateTypeScript(project, "schema/map", "buildSrc/src/main/resources/website/src/app/entity/generated")
}

tasks.register("generateJavaSchemaClasses") {
	Generator.generateJava(project, "schema/data", "core/generated/data", true, "core.data", "core.simulation")
	Generator.generateJava(project, "schema/legacy", "legacy/generated/data", false, "core.data", "legacy.data")
	Generator.generateJava(project, "schema/integration", "core/generated/integration", false, "core.data", "core.integration")
	Generator.generateJava(project, "schema/map", "core/generated/map", false, "core.map")
	Generator.generateJava(project, "schema/oba", "core/generated/oba", false, "core.oba")
	Generator.generateJava(project, "schema/operation", "core/generated/operation", false, "core.data", "core.operation")
	WebserverSetup.setup(project.rootDir, "", "core")
}

tasks.withType(AbstractArchiveTask).configureEach {
	preserveFileTimestamps = false
	reproducibleFileOrder = true
}
