package org.mtr.core.data;

import it.unimi.dsi.fastutil.longs.Long2ObjectOpenHashMap;
import it.unimi.dsi.fastutil.longs.LongArrayList;
import org.mtr.core.generated.data.RoutePlatformDataSchema;
import org.mtr.core.serializer.ReaderBase;

public final class RoutePlatformData extends RoutePlatformDataSchema {

	public Platform platform;

	public RoutePlatformData(long platformId) {
		super(platformId);
		// Initialize platformIds with the single platform for backwards compatibility
		if (platformIds.isEmpty()) {
			platformIds.add(platformId);
		}
	}

	public RoutePlatformData(long mainPlatformId, LongArrayList platformIds) {
		super(mainPlatformId);
		// Clear the existing list and add all provided platform IDs
		this.platformIds.clear();
		if (platformIds.isEmpty()) {
			this.platformIds.add(mainPlatformId);
		} else {
			this.platformIds.addAll(platformIds);
		}
	}

	public RoutePlatformData(ReaderBase readerBase) {
		super(readerBase);
		updateData(readerBase);
		// Note: Backwards compatibility is now handled in getPlatformIdsWithFallback() method
		// to avoid modifying the actual data during deserialization
	}

	public Platform getPlatform() {
		return platform;
	}

	public String getCustomDestination() {
		return customDestination;
	}

	public void setCustomDestination(String customDestination) {
		this.customDestination = customDestination;
	}

	/**
	 * Get the list of platform IDs for this stop.
	 * Returns the raw list without backwards compatibility modifications.
	 * Use getPlatformIdsWithFallback() for backwards compatibility.
	 */
	public LongArrayList getPlatformIds() {
		return platformIds;
	}

	/**
	 * Get the list of platform IDs for this stop with backwards compatibility.
	 * If the list is empty and we have a main platform ID, returns a list with the main platform.
	 */
	public LongArrayList getPlatformIdsWithFallback() {
		if (platformIds.isEmpty() && platformId != 0) {
			// For legacy data that hasn't been migrated yet, return a temporary list
			final LongArrayList fallbackList = new LongArrayList();
			fallbackList.add(platformId);
			return fallbackList;
		}
		return platformIds;
	}

	/**
	 * Check if this stop has multiple platforms configured.
	 */
	public boolean isMultiPlatformStop() {
		return getPlatformIdsWithFallback().size() > 1;
	}

	/**
	 * Set the platform IDs for this stop.
	 * The first platform ID in the list becomes the main platform ID.
	 */
	public void setPlatformIds(LongArrayList newPlatformIds) {
		this.platformIds.clear();
		if (newPlatformIds.isEmpty()) {
			// If no platforms provided, use the current main platform
			if (platformId != 0) {
				this.platformIds.add(platformId);
			}
		} else {
			this.platformIds.addAll(newPlatformIds);
		}
	}

	public void writePlatformCache(Route route, Long2ObjectOpenHashMap<Platform> platformIdMap) {
		platform = platformIdMap.get(platformId);
		if (platform != null) {
			platform.routes.add(route);
			platform.routeColors.add(route.getColor());
		}
	}
}
