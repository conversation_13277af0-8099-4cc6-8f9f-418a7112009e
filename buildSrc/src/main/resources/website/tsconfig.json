/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
	"compileOnSave": false,
	"compilerOptions": {
		"outDir": "./dist/out-tsc",
		"forceConsistentCasingInFileNames": true,
		"strict": true,
		"noImplicitOverride": true,
		"noPropertyAccessFromIndexSignature": true,
		"noImplicitReturns": true,
		"noFallthroughCasesInSwitch": true,
		"skipLibCheck": true,
		"esModuleInterop": true,
		"sourceMap": true,
		"declaration": false,
		"emitDecoratorMetadata": true,
		"experimentalDecorators": true,
		"moduleResolution": "node",
		"importHelpers": true,
		"target": "ES2022",
		"module": "ES2022",
		"allowJs": true,
		"useDefineForClassFields": false,
		"lib": [
			"ES2022",
			"dom"
		]
	},
	"angularCompilerOptions": {
		"enableI18nLegacyMessageIdFormat": false,
		"strictInjectionParameters": true,
		"strictInputAccessModifiers": true,
		"strictTemplates": true
	}
}
