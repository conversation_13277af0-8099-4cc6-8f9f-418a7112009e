<div class="column center loading-background">
	<mat-spinner class="grayscale"/>
</div>
<div class="wrapper" #wrapper [style.opacity]="loading ? 0 : 1">
	<canvas #canvas></canvas>
	<div>
		@for (textLabel of textLabels; track $index) {
			<div class="column center label" [style.transform]="'translate(-50%,0) translate(' + textLabel.x + 'px, ' + textLabel.y + 'px)'"
				 (mousedown)="stationClicked.emit(textLabel.id)"
				 (mouseenter)="textLabel.hoverOverride = true"
				 (mouseleave)="textLabel.hoverOverride = false">
				<div [style.width]="textLabel.stationWidth + 'px'" [style.height]="textLabel.stationHeight + 'px'"></div>
				@if (textLabel.shouldRenderText || textLabel.hoverOverride) {
					<div class="column center">
						@for (textLabelPart of textLabel.text | splitName; track $index) {
							<div class="station-name text {{textLabelPart.isCjk?'cjk':''}}">{{ textLabelPart.text }}</div>
						}
						<div class="row">
							@for (icon of textLabel.icons; track $index) {
								<mat-icon class="station-name icon">{{ icon }}</mat-icon>
							}
						</div>
					</div>
				}
			</div>
		}
	</div>
</div>
<div #stats></div>
