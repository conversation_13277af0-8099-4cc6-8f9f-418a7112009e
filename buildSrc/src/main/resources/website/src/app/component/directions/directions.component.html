<div class="column wrapper">
	<div class="column gap padding-sides">
		<div class="row gap-small center">
			<div class="column gap spacing">
				<app-search #startStationSearch label="Start Station" [includeRoutes]="false" [setText]="setStartStationText" (stationClicked)="onClickStation($event, true)" (textCleared)="onClickStation(undefined, true)"/>
				<app-search #endStationSearch label="End Station" [includeRoutes]="false" [setText]="setEndStationText" (stationClicked)="onClickStation($event, false)" (textCleared)="onClickStation(undefined, false)"/>
			</div>
			<button mat-icon-button (click)="swapStations(startStationSearch, endStationSearch)" [disabled]="!startStationSearch.getText() && !endStationSearch.getText()" matTooltip="Swap Stations">
				<mat-icon>swap_vert</mat-icon>
			</button>
		</div>
		<mat-divider/>
		<mat-form-field subscriptSizing="dynamic">
			<mat-label>Maximum Walking Distance (m)</mat-label>
			<input matInput #maxWalkingDistance type="number" aria-label="Maximum Walking Distance (m)" min="0" max="1000000000" [value]="getDefaultMaxWalkingDistance()" (input)="updateMaxWalkingDistance(maxWalkingDistance.value)"/>
		</mat-form-field>
		<div class="row gap center">
			<mat-checkbox class="grayscale spacing" #refresh [formControl]="automaticRefresh">Automatic Refresh</mat-checkbox>
			<button mat-icon-button (click)="refreshDirections()" [disabled]="refresh.checked || !isValid()" matTooltip="Refresh Directions">
				<mat-icon>refresh</mat-icon>
			</button>
		</div>
	</div>
	@if (isLoading()) {
		<div class="column center wrapper">
			<div class="spacing"></div>
			<mat-spinner class="grayscale"/>
			<div class="spacing"></div>
		</div>
	} @else {
		@if (isValid() && getDirections().length === 0) {
			<div class="column center wrapper">
				<div class="spacing"></div>
				<mat-icon class="big-icon">remove_road</mat-icon>
				<div>No directions found.</div>
				<div class="spacing"></div>
			</div>
		} @else {
			<div class="column padding-sides content">
				@for (direction of getDirections(); track $index) {
					<app-route-display [colorAbove]="$index === 0 ? undefined : getRouteColor($index - 1)" [colorBelow]="getRouteColor($index)" [isStation]="true" [icons]="[]">
						<div class="row center">
							<app-data-list-entry class="spacing" [icons]="[]" [title]="[getStationName(direction.startPosition, direction.startRoutePlatform), '']" [subtitles]="[[getPlatformName(direction.startRoutePlatform), '']]" [useLightColor]="false" [clickable]="false"/>
							<div class="gray no-wrap">{{ direction.startTime | formatDate }}</div>
						</div>
					</app-route-display>
					<app-route-display [colorAbove]="getRouteColor($index)" [colorBelow]="getRouteColor($index)" [isStation]="false" [icons]="[{icon: direction.route ? direction.icon : sameStation(direction) ? 'transfer_within_a_station' : 'directions_walk', offset: 0}]">
						@if (direction.route) {
							<div class="row center">
								<app-data-list-entry class="spacing" [icons]="[getCircularIcon(direction.route)]" [title]="[getRouteName(direction.route), '']" [subtitles]="[[getRouteDestination(direction.route), '']]" [useLightColor]="true" [clickable]="false"/>
								<div class="row center gray">
									<mat-icon inline>schedule</mat-icon>
									<div>&nbsp;{{ getDuration(direction) }}</div>
								</div>
							</div>
						} @else {
							<div class="row center">
								<app-data-list-entry class="spacing" [icons]="[]" [title]="[getDistanceLabel(direction), '']" [subtitles]="[]" [useLightColor]="true" [clickable]="false"/>
								<div class="row center gray">
									<mat-icon inline>schedule</mat-icon>
									<div>&nbsp;{{ getDuration(direction) }}</div>
								</div>
							</div>
						}
					</app-route-display>
					@if (direction.intermediateRoutePlatforms.length > 0) {
						<app-route-display [colorAbove]="getRouteColor($index)" [colorBelow]="getRouteColor($index)" [isStation]="false" [icons]="[]">
							<mat-expansion-panel class="expansion-panel-margin">
								<mat-expansion-panel-header>{{ direction.intermediateRoutePlatforms.length }} intermediate station{{ direction.intermediateRoutePlatforms.length > 1 ? 's' : '' }}</mat-expansion-panel-header>
								<div class="column gap-small">
									@for (intermediateRoutePlatform of direction.intermediateRoutePlatforms; track $index) {
										<div>{{ intermediateRoutePlatform.station.name | formatName }}</div>
									}
								</div>
							</mat-expansion-panel>
						</app-route-display>
					}
					@if ($index === $count - 1) {
						<app-route-display [colorAbove]="getRouteColor($index)" [colorBelow]="undefined" [isStation]="true" [icons]="[]">
							<div class="row center">
								<app-data-list-entry class="spacing" [icons]="[]" [title]="[getStationName(direction.endPosition, direction.endRoutePlatform), '']" [subtitles]="[[getPlatformName(direction.endRoutePlatform), '']]" [useLightColor]="false" [clickable]="false"/>
								<div class="gray no-wrap">{{ direction.endTime | formatDate }}</div>
							</div>
						</app-route-display>
					}
				}
			</div>
		}
	}
</div>
