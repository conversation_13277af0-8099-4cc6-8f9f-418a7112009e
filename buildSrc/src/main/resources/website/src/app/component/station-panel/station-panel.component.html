<div class="column gap wrapper">
	<div class="column gap padding-sides">
		<app-title [name]="getStation()?.name ?? ''" [color]="getStationColor()"/>
		<div class="row gap-small center">
			<div class="row gap center" matTooltip="Location">
				<mat-icon>my_location</mat-icon>
				<div>({{ getCoordinatesText() }})</div>
			</div>
			<div class="row center">
				<button mat-icon-button (click)="copyLocation(copyIconButton)" matTooltip="Copy Location">
					<mat-icon #copyIconButton>content_copy</mat-icon>
				</button>
				<button mat-icon-button (click)="focus()" matTooltip="Focus on Map">
					<mat-icon>filter_center_focus</mat-icon>
				</button>
			</div>
		</div>
		<div class="row gap center">
			<div class="row gap center" matTooltip="Fare Zones">
				<mat-icon>sell</mat-icon>
				<div>({{ getZoneText() }})</div>
			</div>
		</div>
		<div class="row center">
			<mat-icon matTooltip="Directions">directions</mat-icon>
			<button mat-button (click)="openDirections(true)">Start</button>
			<button mat-button (click)="openDirections(false)">End</button>
		</div>
	</div>

	<mat-tab-group class="wrapper" (selectedTabChange)="resetArrivalFilter()">
		<mat-tab label="Arrivals">
			<ng-template matTabContent>
				@if (isLoading()) {
					<div class="column gap center">
						<div></div>
						<div></div>
						<mat-spinner class="grayscale"/>
					</div>
				} @else {
					<div class="column gap-small padding-top-bottom padding-sides content">
						<mat-chip-listbox class="chip" #chipList aria-label="Routes" multiple>
							@for (route of getActiveRoutes(); track $index) {
								<mat-chip-option [style.box-shadow]="'0 0 0 2px ' + (route.color | formatColor) + ' inset'" [style.height]="(route.textLineCount + 1) + 'em'" [id]="route.key" (selectionChange)="updateArrivalFilter(chipList, showTerminatingCheckbox.checked)">
									<div class="row gap-small center">
										<mat-icon>{{ route.typeIcon }}</mat-icon>
										<div class="column chip-text">
											@for (name of route.name | splitName; track $index) {
												<div>{{ name.text }}</div>
											}
										</div>
										@if (route.number !== '') {
											<mat-divider vertical [style.height]="route.textLineCount + 'em'"/>
										}
										@if (route.number !== '') {
											<div class="column chip-text">
												@for (number of route.number | splitName; track $index) {
													<div>{{ number.text }}</div>
												}
											</div>
										}
									</div>
								</mat-chip-option>
							}
						</mat-chip-listbox>
						<mat-checkbox class="grayscale" #showTerminatingCheckbox [style.display]="getHasTerminating() ? '' : 'none'" (change)="updateArrivalFilter(chipList, showTerminatingCheckbox.checked)">Show Terminating</mat-checkbox>

						<div class="column">
							@for (arrival of getArrivals(); track $index) {
								<div class="column">
									<mat-divider/>
									<app-data-list-entry [icons]="[arrival.routeTypeIcon, getCircularStateIcon(arrival.circularState)]" [title]="[arrival.destination | formatName, arrival.getArrivalTime() | formatTime : 'Arrived']" [subtitles]="[
										[(arrival.routeName | formatName) + ' ' + (arrival.routeNumber | formatName), arrival.isContinuous ? 'Every 8 Seconds' : arrival.arrival | formatDate],
										['Platform ' + arrival.platformName, arrival.isContinuous ? '' : (arrival.getDeviation() | formatTime :'') + ' ' + arrival.getDeviationString()]
									]" [color]="arrival.routeColor | formatColor" [useLightColor]="!arrival.realtime" [clickable]="true" (entryClicked)="showDetails(arrival)"/>
								</div>
							}
						</div>
					</div>
				}
			</ng-template>
		</mat-tab>

		<mat-tab label="Routes">
			<ng-template matTabContent>
				<div class="column gap-small padding-top-bottom padding-sides content">
					<mat-checkbox class="grayscale" #showDetailsCheckbox>Show Details</mat-checkbox>
					<div class="column">
						@for (route of getRoutes(); track $index) {
							<div class="column">
								<mat-divider/>
								<app-data-list-entry [icons]="[route.typeIcon, '']" [title]="[route.name | formatName, route.number | formatName]" [subtitles]="showDetailsCheckbox.checked ? mapRouteVariations(route.variations) : []" [color]="route.color | formatColor" [useLightColor]="false" [clickable]="true" (entryClicked)="routeClicked.emit(getRouteKey(route))"/>
							</div>
						}
					</div>
				</div>
			</ng-template>
		</mat-tab>

		@if (getConnections().length > 0) {
			<mat-tab label="Connections">
				<ng-template matTabContent>
					<div class="column padding-top-bottom padding-sides content">
						@for (connection of getConnections(); track $index) {
							<div class="column">
								@if ($index > 0) {
									<mat-divider/>
								}
								<app-data-list-entry [icons]="connection.getIcons()" [title]="[connection.name | formatName, '']" [subtitles]="[]" [color]="connection.color | formatColor" [useLightColor]="false" [clickable]="true" (entryClicked)="stationClicked.emit(connection.id)"/>
							</div>
						}
					</div>
				</ng-template>
			</mat-tab>
		}
	</mat-tab-group>
</div>
