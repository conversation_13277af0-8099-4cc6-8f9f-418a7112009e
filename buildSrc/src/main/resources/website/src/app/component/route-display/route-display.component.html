<div class="row center">
	<div class="display-container" [style.height]="(getHeight() + 4) + 'px'">
		@if (colorAbove === colorBelow) {
			@if (colorAbove === -1) {
				<div class="line thin middle"></div>
			} @else if (colorAbove !== undefined) {
				<div class="line middle" [style.background-color]="colorAbove | formatColor"></div>
			}
		} @else {
			@if (colorAbove === -1) {
				<div class="line thin above"></div>
			} @else if (colorAbove !== undefined) {
				<div class="line above" [style.background-color]="colorAbove | formatColor"></div>
			}
			@if (colorBelow === -1) {
				<div class="line thin below"></div>
			} @else if (colorBelow !== undefined) {
				<div class="line below" [style.background-color]="colorBelow | formatColor"></div>
			}
		}
		@if (isStation) {
			<div class="circle" [style.border-color]="isDarkTheme() ? 'white' : 'black'" [style.background-color]="isDarkTheme() ? 'black' : 'white'"></div>
		}
		@for (icon of icons; track $index) {
			<div [class]="'icon ' + (icon.offset === 0 ? '' : 'light')" [style.top]="(icon.offset + getHeight() / 2 - 12) + 'px'" [matTooltip]="icon.tooltip">
				<mat-icon>{{ icon.icon }}</mat-icon>
			</div>
		}
	</div>
	<div class="full" #text>
		<ng-content/>
	</div>
</div>
