canvas {
	background: var(--mat-sys-surface);
	position: absolute;
	transition: opacity 0.2s;
	width: 100%;
	height: 100%;
}

.label {
	cursor: pointer;
	position: absolute;
	transition: color 0.2s;
	will-change: transform;
}

.label:hover {
	color: #777777;
}

.station-name {
	font-size: 0.6em;
	line-height: 1.2em;
	white-space: nowrap;
	text-shadow: -2px -2px 0 var(--mat-sys-surface),
	0 -2px 0 var(--mat-sys-surface),
	2px -2px 0 var(--mat-sys-surface),
	2px 0 0 var(--mat-sys-surface),
	2px 2px 0 var(--mat-sys-surface),
	0 2px 0 var(--mat-sys-surface),
	-2px 2px 0 var(--mat-sys-surface),
	-2px 0 0 var(--mat-sys-surface);
}

.station-name.cjk {
	font-size: 1.2em;
}

.station-name.text {
	font-weight: 600;
}

.station-name.icon {
	font-size: 1.2em;
	width: unset;
}

.loading-background {
	background-color: var(--mat-sys-surface);
	justify-content: center;
	position: absolute;
	width: 100%;
	height: 100%;
}
