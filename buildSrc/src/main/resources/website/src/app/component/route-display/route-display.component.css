.display-container {
	display: flex;
	position: relative;
	min-width: 40px;
}

.line {
	left: 30px;
	position: absolute;
	width: 6px;
}

.line.thin {
	background-color: #777777;
	left: 32px;
	position: absolute;
	width: 2px;
}

.line.middle {
	height: 100%;
	padding: 16px 0;
	top: -8px;
}

.line.above {
	height: 50%;
	top: 0;
}

.line.below {
	bottom: 0;
	height: 50%;
}

.circle {
	border: 2px solid;
	border-radius: 7px;
	box-sizing: border-box;
	left: 26px;
	position: absolute;
	top: 50%;
	transform: translate(0, -50%);
	width: 14px;
	height: 14px;
}

.icon {
	position: absolute;
	width: 24px;
	height: 24px;
}

.icon.light {
	color: rgba(127, 127, 127, 80%);
}

.full {
	width: 100%;
}
