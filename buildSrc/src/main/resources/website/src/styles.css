* {
	margin: 0;
	padding: 0;
	user-select: none;
}

html, body {
	background: var(--mat-sys-surface);
	color: var(--mat-sys-on-surface);
	overflow: hidden;
	width: 100%;
	height: 100%;
}

div {
	font-family: "Noto Sans", "Noto Serif TC", "Noto Serif SC", "Noto Serif JP", "Noto Serif KR", sans-serif;
}

.light-theme {
	color-scheme: light;
}

.dark-theme {
	color-scheme: dark;
}

.grayscale {
	filter: grayscale(1);
}

.column {
	display: flex;
	flex-flow: column nowrap;
}

.column-reverse {
	display: flex;
	flex-flow: column-reverse nowrap;
}

.row {
	display: flex;
	flex-flow: row nowrap;
}

.row-reverse {
	display: flex;
	flex-flow: row-reverse nowrap;
}

.gap {
	gap: 1em;
}

.gap-small {
	gap: 0.5em;
}

.center {
	align-items: center;
}

.spacing {
	flex-grow: 1;
}

.wrapper {
	overflow: hidden;
	width: 100%;
	height: 100%;
}

.content {
	overflow-x: hidden;
	overflow-y: auto;
	width: 100%;
	height: 100%;
}

.padding-top-bottom {
	box-sizing: border-box;
	padding-top: 1em;
	padding-bottom: 1em;
}

.padding-sides {
	box-sizing: border-box;
	padding-left: 1em;
	padding-right: 1em;
}

.padding-top-bottom-small {
	box-sizing: border-box;
	padding-top: 0.5em;
	padding-bottom: 0.5em;
}

.padding-sides-small {
	box-sizing: border-box;
	padding-left: 0.5em;
	padding-right: 0.5em;
}

mat-icon {
	min-width: 1em;
}
