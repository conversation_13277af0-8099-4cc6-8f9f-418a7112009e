{"name": "website", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0", "build": "ng build --base-href a", "watch": "ng build --watch --configuration development", "test": "ng test", "update-all": "ng update @angular/core @angular/cli --allow-dirty && ng update @angular/material --allow-dirty && npm update --all --save --force && npm install --force"}, "private": true, "dependencies": {"@angular/animations": "^19.0.4", "@angular/cdk": "^19.0.3", "@angular/common": "^19.0.4", "@angular/compiler": "^19.0.4", "@angular/core": "^19.0.4", "@angular/forms": "^19.0.4", "@angular/material": "^19.0.3", "@angular/platform-browser": "^19.0.4", "@angular/platform-browser-dynamic": "^19.0.4", "@angular/router": "^19.0.4", "@types/three": "^0.163.0", "rxjs": "~7.8.1", "three": "^0.164.1", "tslib": "^2.8.0", "zone.js": "^0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.5", "@angular/cli": "^19.0.5", "@angular/compiler-cli": "^19.0.4", "@types/jasmine": "~5.1.4", "jasmine-core": "~5.1.2", "karma": "~6.4.3", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.6.3"}}