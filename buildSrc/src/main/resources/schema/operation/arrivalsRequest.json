{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "javaImplements": ["SerializedDataBase"], "properties": {"platformIds": {"type": "array", "items": {"type": "integer"}}, "platformIdsHex": {"type": "array", "items": {"type": "string"}}, "stationIds": {"type": "array", "items": {"type": "integer"}}, "stationIdsHex": {"type": "array", "items": {"type": "string"}}, "maxCountPerPlatform": {"type": "integer", "minimum": 1}, "maxCountTotal": {"type": "integer", "minimum": 0}}, "required": ["maxCountPerPlatform", "maxCountTotal"]}