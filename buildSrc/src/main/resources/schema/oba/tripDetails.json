{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "javaImplements": ["SerializedDataBase"], "properties": {"tripId": {"type": "string"}, "serviceDate": {"type": "integer", "minimum": 0}, "frequency": {"$ref": "frequency.json"}, "status": {"$ref": "tripStatus.json"}, "schedule": {"$ref": "schedule.json"}, "situationIds": {"type": "array", "items": {"type": "string"}}}, "required": ["tripId", "serviceDate", "status", "schedule"]}