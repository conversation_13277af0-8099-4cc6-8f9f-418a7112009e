{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "javaImplements": ["SerializedDataBase"], "properties": {"tripId": {"type": "string"}, "blockStopTimes": {"type": "array", "items": {"$ref": "blockStopTime.json"}}, "accumulatedSlackTime": {"type": "integer", "minimum": 0}, "distanceAlongBlock": {"type": "number", "minimum": 0}}, "required": ["tripId", "accumulatedSlackTime", "distanceAlongBlock"]}