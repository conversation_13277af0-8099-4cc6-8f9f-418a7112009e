{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "javaImplements": ["SerializedDataBase"], "properties": {"stopId": {"type": "string"}, "arrivalTime": {"type": "integer", "minimum": 0}, "departureTime": {"type": "integer", "minimum": 0}, "pickupType": {"type": "integer", "minimum": 0}, "dropOffType": {"type": "integer", "minimum": 0}, "stopHeadsign": {"type": "string"}}, "required": ["stopId", "arrivalTime", "departureTime", "pickupType", "dropOffType", "stopHeadsign"]}