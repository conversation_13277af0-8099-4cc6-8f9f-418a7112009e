{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "javaImplements": ["SerializedDataBase"], "properties": {"id": {"type": "string"}, "agencyId": {"type": "string"}, "shortName": {"type": "string"}, "longName": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "integer", "minimum": 0}, "url": {"type": "string"}, "color": {"type": "string"}, "textColor": {"type": "string"}}, "required": ["id", "agencyId", "shortName", "<PERSON><PERSON><PERSON>", "description", "type", "url", "color", "textColor"]}