{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "javaImplements": ["SerializedDataBase"], "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "url": {"type": "string"}, "timezone": {"type": "string"}, "lang": {"type": "string"}, "phone": {"type": "string"}, "fareUrl": {"type": "string"}, "email": {"type": "string"}}, "required": ["id", "name", "url", "timezone"]}