{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "javaImplements": ["SerializedDataBase"], "properties": {"vehicleId": {"type": "string"}, "lastUpdateTime": {"type": "integer", "minimum": 0}, "lastLocationUpdateTime": {"type": "integer", "minimum": 0}, "location": {"$ref": "position.json"}, "tripId": {"type": "string"}, "tripStatus": {"$ref": "tripStatus.json"}}, "required": ["vehicleId", "lastUpdateTime", "lastLocationUpdateTime", "location", "tripId", "tripStatus"]}