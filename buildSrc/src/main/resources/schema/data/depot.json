{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "javaExtends": "AreaBase<Depot, Siding>", "extends": {"$ref": "areaBase.json"}, "properties": {"routeIds": {"type": "array", "items": {"type": "integer"}}, "lastGeneratedMillis": {"type": "integer", "minimum": 0}, "lastGeneratedStatus": {"$ref": "Depot.GeneratedStatus"}, "lastGeneratedFailedStartId": {"type": "integer"}, "lastGeneratedFailedEndId": {"type": "integer"}, "lastGeneratedFailedSidingCount": {"type": "integer"}, "useRealTime": {"type": "boolean"}, "frequencies": {"type": "array", "minItems": 24, "maxItems": 24, "items": {"type": "integer", "minimum": 0}}, "realTimeDepartures": {"type": "array", "items": {"type": "integer"}}, "repeatInfinitely": {"type": "boolean"}, "cruisingAltitude": {"type": "integer", "default": 256}}}