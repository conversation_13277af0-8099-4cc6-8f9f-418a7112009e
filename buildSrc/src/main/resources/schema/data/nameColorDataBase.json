{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "javaImplements": ["SerializedDataBase"], "javaConstructorFields": ["data"], "properties": {"id": {"type": "integer", "default": "new java.util.Random().nextLong()"}, "transportMode": {"$ref": "TransportMode"}, "name": {"type": "string"}, "color": {"type": "integer", "minimum": 0, "maximum": 16777215}}, "required": ["id", "transportMode"]}