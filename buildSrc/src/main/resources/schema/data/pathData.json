{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "javaImplements": ["SerializedDataBase"], "properties": {"savedRailBaseId": {"type": "integer"}, "dwellTime": {"type": "integer", "minimum": 1}, "stopIndex": {"type": "integer"}, "startDistance": {"type": "number"}, "endDistance": {"type": "number"}, "startPosition": {"$ref": "position.json"}, "startAngle": {"$ref": "<PERSON><PERSON>"}, "endPosition": {"$ref": "position.json"}, "endAngle": {"$ref": "<PERSON><PERSON>"}, "shape": {"$ref": "Rail.Shape"}, "verticalRadius": {"type": "number"}, "speedLimit": {"type": "integer"}}, "required": ["savedRailBaseId", "dwellTime", "stopIndex", "startDistance", "endDistance", "startPosition", "startAngle", "endPosition", "endAngle"]}